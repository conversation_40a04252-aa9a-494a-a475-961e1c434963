import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, TouchableOpacity } from 'react-native';
import { colors } from '../constants/colors';

// Import tab screens
import DashboardNavigator from './DashboardNavigator';
import AccountsNavigator from './AccountsNavigator';
import TransactionsNavigator from './TransactionsNavigator';
import GoalsNavigator from './GoalsNavigator';
import MoreNavigator from './MoreNavigator';

// Import drawer screens
import StatementImportScreen from '../screens/statements/StatementImportScreen';
import NotificationCenterScreen from '../screens/notifications/NotificationCenterScreen';
import RecommendationsHistoryScreen from '../screens/recommendations/RecommendationsHistoryScreen';
import PremiumUpgradeScreen from '../screens/premium/PremiumUpgradeScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import AccountSharesScreen from '../screens/premium/AccountSharesScreen';
import HelpSupportScreen from '../screens/help/HelpSupportScreen';

const Drawer = createDrawerNavigator();
const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Tab Bar Icon Component
const TabBarIcon: React.FC<{ name: string; focused: boolean }> = ({ name, focused }) => {
  const iconMap: { [key: string]: string } = {
    Dashboard: '🏠',
    Accounts: '💼',
    Transactions: '📋',
    Goals: '🎯',
    More: '☰',
  };

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <Text style={{ fontSize: 24, opacity: focused ? 1 : 0.6 }}>
        {iconMap[name] || '📱'}
      </Text>
      <Text
        style={{
          fontSize: 12,
          color: focused ? colors.primary : colors.textSecondary,
          marginTop: 2,
        }}
      >
        {name}
      </Text>
    </View>
  );
};

// Bottom Tab Navigator
const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => (
          <TabBarIcon name={route.name} focused={focused} />
        ),
        tabBarShowLabel: false,
        tabBarStyle: {
          backgroundColor: colors.background,
          borderTopColor: colors.border,
          height: 80,
          paddingBottom: 10,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardNavigator} />
      <Tab.Screen name="Accounts" component={AccountsNavigator} />
      <Tab.Screen name="Transactions" component={TransactionsNavigator} />
      <Tab.Screen name="Goals" component={GoalsNavigator} />
      <Tab.Screen name="More" component={MoreNavigator} />
    </Tab.Navigator>
  );
};

// Main Navigator with Drawer
const MainNavigator: React.FC = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: colors.background,
          width: 280,
        },
        drawerActiveTintColor: colors.primary,
        drawerInactiveTintColor: colors.textSecondary,
      }}
    >
      <Drawer.Screen
        name="Home"
        component={TabNavigator}
        options={{
          drawerLabel: 'Home',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>🏠</Text>,
        }}
      />
      <Drawer.Screen
        name="StatementImport"
        component={StatementImportScreen}
        options={{
          drawerLabel: 'Statement Import',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>📄</Text>,
        }}
      />
      <Drawer.Screen
        name="NotificationCenter"
        component={NotificationCenterScreen}
        options={{
          drawerLabel: 'Notifications',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>🔔</Text>,
        }}
      />
      <Drawer.Screen
        name="RecommendationsHistory"
        component={RecommendationsHistoryScreen}
        options={{
          drawerLabel: 'Recommendations',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>💡</Text>,
        }}
      />
      <Drawer.Screen
        name="PremiumUpgrade"
        component={PremiumUpgradeScreen}
        options={{
          drawerLabel: 'Premium Upgrade',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>⭐</Text>,
        }}
      />
      <Drawer.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          drawerLabel: 'Settings',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>⚙️</Text>,
        }}
      />
      <Drawer.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          drawerLabel: 'Profile',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>👤</Text>,
        }}
      />
      <Drawer.Screen
        name="AccountShares"
        component={AccountSharesScreen}
        options={{
          drawerLabel: 'Account Shares',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>🤝</Text>,
        }}
      />
      <Drawer.Screen
        name="HelpSupport"
        component={HelpSupportScreen}
        options={{
          drawerLabel: 'Help & Support',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>❓</Text>,
        }}
      />
    </Drawer.Navigator>
  );
};

export default MainNavigator;